'use client'

import React, { Suspense } from 'react'
import { useSearchParams } from 'next/navigation'
import { ReactLenis } from 'lenis/react'
import { PageNav } from '@/components/common/PageNav'
import { ColorTemplate } from '@/components/sections/styles/shared/themeConfig'
import SimpleFooter from '@/components/footer/layouts/SimpleFooter'
import { getFunAndTrendySimpleFooterStyle } from '@/components/footer/styles/simple/simplefooter-funandtrendy'

function FunAndTrendySimpleFooterContent() {
  const searchParams = useSearchParams()
  const theme = (searchParams.get('theme') as unknown as ColorTemplate) || 1
  const heroStyle = getFunAndTrendySimpleFooterStyle(Number(theme) as ColorTemplate)
  
  return (
    <ReactLenis root>
      <PageNav />
      <div className="relative z-10 h-screen flex items-center justify-center">
        <p className="text-3xl">Scroll down to see the simple footer</p>
      </div>
      <SimpleFooter style={heroStyle} />
    </ReactLenis>
  )
}

export default function FunAndTrendyPage() {
  return (
    <Suspense>
      <FunAndTrendySimpleFooterContent />
    </Suspense>
  )
}