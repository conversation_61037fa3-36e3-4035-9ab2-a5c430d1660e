'use client'

import React, { Suspense } from 'react'
import { ReactLenis } from 'lenis/react'
import { PageNav } from '@/components/common/PageNav'
import { useSearchParams } from 'next/navigation'
import { ColorTemplate } from '@/components/sections/styles/shared/themeConfig'
import { getFuturisticSimpleFooterStyle } from '@/components/footer/styles/simple/simplefooter-futuristic'
import SimpleFooter from '@/components/footer/layouts/SimpleFooter'

function FuturisticAndOutOfBoxContent() {
  const searchParams = useSearchParams()
  const theme = (Number(searchParams.get('theme')) || 1) as ColorTemplate
  const style = getFuturisticSimpleFooterStyle(theme)
  
  return <SimpleFooter style={style} />
}

export default function FuturisticAndOutOfBoxPage() {
  return (
    <ReactLenis root>
      <PageNav />
      <div className="relative z-10 h-screen flex items-center justify-center">
        <p className="text-3xl">Scroll down to see the simple footer</p>
      </div>
      <Suspense>
        <FuturisticAndOutOfBoxContent />
      </Suspense>
    </ReactLenis>
  )
}