'use client'

import React, { Suspense } from 'react'
import { useSearchParams } from 'next/navigation'
import { ReactLenis } from 'lenis/react'
import { PageNav } from '@/components/common/PageNav'
import { ColorTemplate } from '@/components/sections/styles/shared/themeConfig'
import { getFunAndTrendylogoFooterStyle } from '@/components/footer/styles/logo/logofooter-funandtrendy'
import LogoFooter from '@/components/footer/layouts/LogoFooter'

function FunAndTrendySimpleFooterContent() {
  const searchParams = useSearchParams()
  const theme = (searchParams.get('theme') as unknown as ColorTemplate) || 1
  const heroStyle = getFunAndTrendylogoFooterStyle(Number(theme) as ColorTemplate)
  
  return (
    <ReactLenis root>
      <PageNav />
      <div className="h-screen flex items-center justify-center">
        <p className="text-3xl">Scroll down to see the simple footer</p>
      </div>
      <LogoFooter style={heroStyle} />
    </ReactLenis>
  )
}

export default function FunAndTrendyPage() {
  return (
    <Suspense>
      <FunAndTrendySimpleFooterContent />
    </Suspense>
  )
}