'use client'

import React, { Suspense } from 'react'
import { ReactLenis } from 'lenis/react'
import { PageNav } from '@/components/common/PageNav'
import { useSearchParams } from 'next/navigation'
import { ColorTemplate } from '@/components/sections/styles/shared/themeConfig'
import { getFuturisticgradientFooterStyle } from '@/components/footer/styles/gradient/gradientfooter-futuristic'
import GradientFooter from '@/components/footer/layouts/GradientFooter'

function FuturisticAndOutOfBoxContent() {
  const searchParams = useSearchParams()
  const theme = (Number(searchParams.get('theme')) || 1) as ColorTemplate
  const style = getFuturisticgradientFooterStyle(theme)
  
  return <GradientFooter style={style} />
}

export default function FuturisticAndOutOfBoxPage() {
  return (
    <ReactLenis root>
      <PageNav />
      <div className="h-screen flex items-center justify-center">
        <p className="text-3xl">Scroll down to see the simple footer</p>
      </div>
      <Suspense>
        <FuturisticAndOutOfBoxContent />
      </Suspense>
    </ReactLenis>
  )
}