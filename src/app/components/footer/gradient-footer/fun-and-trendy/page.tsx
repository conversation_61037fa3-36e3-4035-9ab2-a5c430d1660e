'use client'

import React, { Suspense } from 'react'
import { useSearchParams } from 'next/navigation'
import { ReactLenis } from 'lenis/react'
import { PageNav } from '@/components/common/PageNav'
import { ColorTemplate } from '@/components/sections/styles/shared/themeConfig'
import { getFunAndTrendygradientFooterStyle } from '@/components/footer/styles/gradient/gradientfooter-funandtrendy'
import GradientFooter from '@/components/footer/layouts/GradientFooter'

function FunAndTrendySimpleFooterContent() {
  const searchParams = useSearchParams()
  const theme = (searchParams.get('theme') as unknown as ColorTemplate) || 1
  const heroStyle = getFunAndTrendygradientFooterStyle(Number(theme) as ColorTemplate)
  
  return (
    <ReactLenis root>
      <PageNav />
      <div className="h-screen flex items-center justify-center">
        <p className="text-3xl">Scroll down to see the simple footer</p>
      </div>
      <GradientFooter style={heroStyle} />
    </ReactLenis>
  )
}

export default function FunAndTrendyPage() {
  return (
    <Suspense>
      <FunAndTrendySimpleFooterContent />
    </Suspense>
  )
}