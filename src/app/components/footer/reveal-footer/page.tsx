'use client';

import React from 'react';
import Link from 'next/link';
import { PageNav } from '@/components/common/PageNav';

const revealfootertyles: Array<{ name: string; href: string }> = [
    {
        name: 'Fun and Trendy',
        href: '/components/footer/reveal-footer/fun-and-trendy'
    },
    {
        name: 'Futuristic and Out of Box',
        href: '/components/footer/reveal-footer/futuristic-and-out-of-box'
    }
];

export default function RevealFooterPage() {
    return (
        <section className="min-h-screen py-[var(--width-10)]">
            <PageNav />
            <div className="w-full px-[var(--width-10)]">
                <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
                    {revealfootertyles.map((style) => (
                        <Link key={style.name} href={style.href}>
                            <div className="aspect-square card relative rounded p-6 flex justify-center items-center text-center cursor-pointer">
                                <h3 className="text-xl">{style.name}</h3>
                            </div>
                        </Link>
                    ))}
                </div>
            </div>
        </section>
    );
}