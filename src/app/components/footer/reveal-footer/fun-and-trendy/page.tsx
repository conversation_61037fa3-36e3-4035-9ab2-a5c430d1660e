'use client'

import React, { Suspense } from 'react'
import { useSearchParams } from 'next/navigation'
import { ReactLenis } from 'lenis/react'
import { PageNav } from '@/components/common/PageNav'
import { ColorTemplate } from '@/components/sections/styles/shared/themeConfig'
import { getFunAndTrendyRevealFooterStyle } from '@/components/footer/styles/reveal/revealfooter-funandtrendy'
import RevealFooter from '@/components/footer/layouts/RevealFooter'

function FunAndTrendySimpleFooterContent() {
  const searchParams = useSearchParams()
  const theme = (searchParams.get('theme') as unknown as ColorTemplate) || 1
  const heroStyle = getFunAndTrendyRevealFooterStyle(Number(theme) as ColorTemplate)
  
  return (
    <ReactLenis root>
      <PageNav />
      <div className="relative z-10 h-screen flex items-center justify-center">
        <p className="text-3xl">Scroll down to see the simple footer</p>
      </div>
      <RevealFooter style={heroStyle} />
    </ReactLenis>
  )
}

export default function FunAndTrendyPage() {
  return (
    <Suspense>
      <FunAndTrendySimpleFooterContent />
    </Suspense>
  )
}