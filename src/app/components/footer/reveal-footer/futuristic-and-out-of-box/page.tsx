'use client'

import React, { Suspense } from 'react'
import { ReactLenis } from 'lenis/react'
import { PageNav } from '@/components/common/PageNav'
import { useSearchParams } from 'next/navigation'
import { ColorTemplate } from '@/components/sections/styles/shared/themeConfig'
import { getFuturisticRevealFooterStyle } from '@/components/footer/styles/reveal/revealfooter-futuristic'
import RevealFooter from '@/components/footer/layouts/RevealFooter'

function FuturisticAndOutOfBoxContent() {
  const searchParams = useSearchParams()
  const theme = (Number(searchParams.get('theme')) || 1) as ColorTemplate
  const style = getFuturisticRevealFooterStyle(theme)
  
  return <RevealFooter style={style} />
}

export default function FuturisticAndOutOfBoxPage() {
  return (
    <ReactLenis root>
      <PageNav />
      <div className="relative z-10 h-screen flex items-center justify-center">
        <p className="text-3xl">Scroll down to see the simple footer</p>
      </div>
      <Suspense>
        <FuturisticAndOutOfBoxContent />
      </Suspense>
    </ReactLenis>
  )
}