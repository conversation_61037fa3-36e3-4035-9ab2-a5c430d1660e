'use client';

import React, { Suspense } from 'react';
import { React<PERSON>enis } from 'lenis/react';
import YearTimeline, { type YearTimelineItem } from '@/components/timeline/YearTimeline';
import { PageNav } from '@/components/common/PageNav';
import { useSearchParams } from 'next/navigation';
import { ColorTemplate } from '@/components/timeline/styles/shared/themeConfig';
import { getFuturisticYearTimelineStyle } from '@/components/timeline/styles/year-timeline/futuristicAndOutOfBox';

function FuturisticYearTimelineContent() {
  const searchParams = useSearchParams();
  const theme = (searchParams.get('theme') as unknown as ColorTemplate) || 1;
  const colorTemplate = Number(theme) as ColorTemplate;
  const style = getFuturisticYearTimelineStyle(colorTemplate);

  const timelineData: YearTimelineItem[] = [
    {
      year: "2021",
      title: "Phase 1: Foundation",
      description: "Initial research and planning phase for the project development including market analysis, stakeholder interviews, competitive assessment, technical feasibility studies, and establishment of core project objectives to ensure successful foundation"
    },
    {
      year: "2022", 
      title: "Phase 2: Design & Prototype",
      description: "Design and prototyping with user feedback integration through iterative design sprints, wireframing, high-fidelity mockups, usability testing, accessibility reviews, and continuous refinement based on user analytics"
    },
    {
      year: "2023",
      title: "Phase 3: Development",
      description: "Development and implementation of core features using agile methodologies, continuous integration, code reviews, performance optimization, API development, cloud infrastructure setup, and progressive feature rollouts"
    },
    {
      year: "2024",
      title: "Phase 4: Launch & Scale",
      description: "Testing, optimization and final deployment encompassing quality assurance protocols, automated testing, performance benchmarking, user acceptance testing, production deployment, post-launch monitoring, and continuous improvements"
    }
  ];

  return (
    <ReactLenis root>
      <div className="min-h-screen">
        <PageNav />
        <div className="h-screen flex items-center justify-center">
          <div className="text-center">
            <h1 className="text-4xl font-bold mb-4">Futuristic Year Timeline</h1>
            <p className="text-base mb-4">Theme: {colorTemplate}</p>
            <p className="text-base">Scroll down to see the timeline effect</p>
          </div>
        </div>
        <YearTimeline items={timelineData} style={style} />
        <div className="h-screen flex items-center justify-center">
          <p className="text-base text-center">The end</p>
        </div>
      </div>
    </ReactLenis>
  );
}

export default function FuturisticYearTimelinePage() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <FuturisticYearTimelineContent />
    </Suspense>
  );
}
