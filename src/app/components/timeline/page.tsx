"use client";

import React from "react";
import Link from "next/link";
import { PageNav } from "@/components/common/PageNav";

const timelineComponents = [
  { name: "Timeline", href: "/components/timeline/timeline" },
  { name: "Popup Timeline", href: "/components/timeline/popup-timeline" },
  {
    name: "Horizontal Timeline",
    href: "/components/timeline/horizontal-timeline",
  },
  { name: "Stack Timeline", href: "/components/timeline/stack-timeline" },
  { name: "3D Stack Timeline", href: "/components/timeline/3d-stack-timeline" },
  { name: "Process Timeline", href: "/components/timeline/process-timeline" },
  { name: "Year Timeline", href: "/components/timeline/year-timeline" },
  { name: "Phone Timeline", href: "/components/timeline/phone-timeline" },
];

const styledTimelineComponents = [
  // Year Timeline Styles
  {
    name: "Year Timeline - Fun & Trendy",
    href: "/components/timeline/year-timeline/fun-and-trendy?theme=1",
    theme: "Theme 1",
  },
  {
    name: "Year Timeline - Fun & Trendy",
    href: "/components/timeline/year-timeline/fun-and-trendy?theme=2",
    theme: "Theme 2",
  },
  {
    name: "Year Timeline - Futuristic",
    href: "/components/timeline/year-timeline/futuristic-and-out-of-box?theme=1",
    theme: "Theme 1",
  },
  {
    name: "Year Timeline - Futuristic",
    href: "/components/timeline/year-timeline/futuristic-and-out-of-box?theme=2",
    theme: "Theme 2",
  },

  // Regular Timeline Styles
  {
    name: "Timeline - Fun & Trendy",
    href: "/components/timeline/timeline/fun-and-trendy?theme=1",
    theme: "Theme 1",
  },
  {
    name: "Timeline - Fun & Trendy",
    href: "/components/timeline/timeline/fun-and-trendy?theme=2",
    theme: "Theme 2",
  },
  {
    name: "Timeline - Futuristic",
    href: "/components/timeline/timeline/futuristic-and-out-of-box?theme=1",
    theme: "Theme 1",
  },
  {
    name: "Timeline - Futuristic",
    href: "/components/timeline/timeline/futuristic-and-out-of-box?theme=2",
    theme: "Theme 2",
  },

  // Process Timeline Styles
  {
    name: "Process Timeline - Fun & Trendy",
    href: "/components/timeline/process-timeline/fun-and-trendy?theme=1",
    theme: "Theme 1",
  },
  {
    name: "Process Timeline - Fun & Trendy",
    href: "/components/timeline/process-timeline/fun-and-trendy?theme=2",
    theme: "Theme 2",
  },
  {
    name: "Process Timeline - Futuristic",
    href: "/components/timeline/process-timeline/futuristic-and-out-of-box?theme=1",
    theme: "Theme 1",
  },
  {
    name: "Process Timeline - Futuristic",
    href: "/components/timeline/process-timeline/futuristic-and-out-of-box?theme=2",
    theme: "Theme 2",
  },
];

export default function TimelinePage() {
  return (
    <section className="min-h-screen py-[var(--width-10)]">
      <PageNav />
      <div className="w-full px-[var(--width-10)] space-y-12">
        {/* Original Timeline Components */}
        <div>
          <h2 className="text-3xl font-bold mb-6">
            Original Timeline Components
          </h2>
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
            {timelineComponents.map((component) => (
              <Link key={component.name} href={component.href}>
                <div className="aspect-square card relative rounded p-6 flex justify-center items-center text-center cursor-pointer">
                  <h3 className="text-xl">{component.name}</h3>
                </div>
              </Link>
            ))}
          </div>
        </div>

        {/* Styled Timeline Components */}
        <div>
          <h2 className="text-3xl font-bold mb-6">
            Styled Timeline Components
          </h2>
          <p className="text-gray-600 mb-6">
            Timeline components with Fun & Trendy and Futuristic styles, each
            with 2 theme variations
          </p>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {styledTimelineComponents.map((component, index) => (
              <Link
                key={`${component.name}-${component.theme}-${index}`}
                href={component.href}
              >
                <div className="card relative rounded p-6 flex flex-col justify-center items-center text-center cursor-pointer min-h-[120px]">
                  <h3 className="text-lg font-semibold mb-2">
                    {component.name}
                  </h3>
                  <span className="text-sm text-gray-500">
                    {component.theme}
                  </span>
                </div>
              </Link>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}
