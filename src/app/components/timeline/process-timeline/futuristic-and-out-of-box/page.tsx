'use client';

import React, { Suspense } from 'react';
import { <PERSON><PERSON><PERSON>eni<PERSON> } from 'lenis/react';
import ProcessTimeline from '@/components/timeline/ProcessTimeline';
import { PageNav } from '@/components/common/PageNav';
import { useSearchParams } from 'next/navigation';
import { ColorTemplate } from '@/components/timeline/styles/shared/themeConfig';
import { getFuturisticProcessTimelineStyle } from '@/components/timeline/styles/process-timeline/futuristicAndOutOfBox';
import type { ProcessTimelineItem } from '@/types/timeline';
import { Award, BarChart2, Eye, Lightbulb, RefreshCw, Target, Zap } from 'lucide-react';

function FuturisticProcessTimelineContent() {
  const searchParams = useSearchParams();
  const theme = (searchParams.get('theme') as unknown as ColorTemplate) || 1;
  const colorTemplate = Number(theme) as ColorTemplate;
  const style = getFuturisticProcessTimelineStyle(colorTemplate);

  const processItems: ProcessTimelineItem[] = [
    {
      id: "01",
      title: "Research & Discovery Phase",
      description: "Initial research and planning phase for the project development including market analysis, user interviews, competitive research, and technical feasibility studies to ensure project success",
      image: "/images/placeholder1.avif",
      items: [
        { icon: Target, text: "Market analysis & user research" },
        { icon: BarChart2, text: "Competitive analysis" },
        { icon: Lightbulb, text: "Technical feasibility studies" }
      ],
      reverse: false
    },
    {
      id: "02",
      title: "Design & Prototyping",
      description: "Creative design process with user-centered approach, including wireframing, visual design, prototyping, and iterative testing to create optimal user experiences",
      image: "/images/placeholder2.avif",
      items: [
        { icon: Eye, text: "User experience design" },
        { icon: RefreshCw, text: "Iterative prototyping" },
        { icon: Award, text: "Design system creation" }
      ],
      reverse: true
    },
    {
      id: "03",
      title: "Development & Implementation",
      description: "Full-scale development using modern technologies and best practices, including agile development, continuous integration, and comprehensive testing protocols",
      image: "/images/placeholder3.avif",
      items: [
        { icon: Zap, text: "Agile development process" },
        { icon: Target, text: "Quality assurance testing" },
        { icon: BarChart2, text: "Performance optimization" }
      ],
      reverse: false
    }
  ];

  return (
    <ReactLenis root>
      <div className="min-h-screen">
        <PageNav />
        <div className="h-screen flex items-center justify-center">
          <div className="text-center">
            <h1 className="text-4xl font-bold mb-4">Futuristic Process Timeline</h1>
            <p className="text-base mb-4">Theme: {colorTemplate}</p>
            <p className="text-base">Scroll down to see the process timeline</p>
          </div>
        </div>
        <ProcessTimeline items={processItems} style={style} />
        <div className="h-screen flex items-center justify-center">
          <p className="text-base text-center">The end</p>
        </div>
      </div>
    </ReactLenis>
  );
}

export default function FuturisticProcessTimelinePage() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <FuturisticProcessTimelineContent />
    </Suspense>
  );
}
