'use client';

import React, { Suspense } from 'react';
import { ReactLenis } from 'lenis/react';
import Timeline from '@/components/timeline/Timeline';
import { PageNav } from '@/components/common/PageNav';
import { useSearchParams } from 'next/navigation';
import { ColorTemplate } from '@/components/timeline/styles/shared/themeConfig';
import { getFunAndTrendyTimelineStyle } from '@/components/timeline/styles/timeline/funAndTrendy';

function FunAndTrendyTimelineContent() {
  const searchParams = useSearchParams();
  const theme = (searchParams.get('theme') as unknown as ColorTemplate) || 1;
  const colorTemplate = Number(theme) as ColorTemplate;
  const style = getFunAndTrendyTimelineStyle(colorTemplate);

  const timelineData = [
    {
      title: "Project Kickoff",
      description: "Initial project planning and team assembly with stakeholder alignment",
      image: "/images/placeholder1.avif"
    },
    {
      title: "Research Phase",
      description: "Market research, user interviews, and competitive analysis",
      image: "/images/placeholder2.avif"
    },
    {
      title: "Design Sprint",
      description: "Wireframing, prototyping, and user experience design",
      image: "/images/placeholder3.avif"
    },
    {
      title: "Development",
      description: "Core feature development and iterative testing",
      image: "/images/placeholder4.avif"
    },
    {
      title: "Launch",
      description: "Product launch and post-launch optimization",
      image: "/images/placeholder1.avif"
    }
  ];

  return (
    <ReactLenis root>
      <div className="min-h-screen">
        <PageNav />
        <div className="h-screen flex items-center justify-center">
          <div className="text-center">
            <h1 className="text-4xl font-bold mb-4">Fun & Trendy Timeline</h1>
            <p className="text-base mb-4">Theme: {colorTemplate}</p>
            <p className="text-base">Scroll down to see the timeline</p>
          </div>
        </div>
        <Timeline items={timelineData} style={style} title="Project Timeline" />
        <div className="h-screen flex items-center justify-center">
          <p className="text-base text-center">The end</p>
        </div>
      </div>
    </ReactLenis>
  );
}

export default function FunAndTrendyTimelinePage() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <FunAndTrendyTimelineContent />
    </Suspense>
  );
}
