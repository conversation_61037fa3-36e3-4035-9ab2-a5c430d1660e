"use client";

import React, { Suspense } from "react";
import { React<PERSON>enis } from "lenis/react";
import { PageNav } from "@/components/common/PageNav";
import { useSearchParams } from "next/navigation";
import { ColorTemplate } from "@/components/sections/styles/shared/themeConfig";
import { getFuturisticFooterStyle } from "@/components/sections/styles/footer/mew/futuristicAndOutOfBox";
import MewFooter from "@/components/sections/layouts/footer/MewFooter";

function FuturisticMewFooterContent() {
  const searchParams = useSearchParams();
  const theme = (Number(searchParams.get("theme")) || 1) as ColorTemplate;
  const style = getFuturisticFooterStyle(theme);

  return <MewFooter style={style} />;
}

export default function FuturisticMewFooterPage() {
  return (
    <ReactLenis root>
      <PageNav position="bottom" />
      <Suspense fallback={<div>Loading...</div>}>
        <FuturisticMewFooterContent />
      </Suspense>
    </ReactLenis>
  );
}
