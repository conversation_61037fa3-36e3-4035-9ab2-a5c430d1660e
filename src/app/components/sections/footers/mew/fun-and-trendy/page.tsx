"use client";

import React, { Suspense } from "react";
import { ReactLenis } from "lenis/react";
import { PageNav } from "@/components/common/PageNav";
import { useSearchParams } from "next/navigation";
import { ColorTemplate } from "@/components/sections/styles/shared/themeConfig";
import { getFunAndTrendyFooterStyle } from "@/components/sections/styles/footer/mew/funAndTrendy";
import MewFooter from "@/components/sections/layouts/footer/MewFooter";

function FunAndTrendyMewFooterContent() {
  const searchParams = useSearchParams();
  const theme = (Number(searchParams.get("theme")) || 1) as ColorTemplate;
  const style = getFunAndTrendyFooterStyle(theme);

  return <MewFooter style={style} />;
}

export default function FunAndTrendyMewFooterPage() {
  return (
    <ReactLenis root>
      <PageNav position="bottom" />
      <Suspense fallback={<div>Loading...</div>}>
        <FunAndTrendyMewFooterContent />
      </Suspense>
    </ReactLenis>
  );
}