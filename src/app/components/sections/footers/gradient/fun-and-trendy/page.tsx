"use client";

import React, { Suspense } from "react";
import { ReactLenis } from "lenis/react";
import { PageNav } from "@/components/common/PageNav";
import { useSearchParams } from "next/navigation";
import { ColorTemplate } from "@/components/sections/styles/shared/themeConfig";
import { getFunAndTrendyFooterStyle } from "@/components/sections/styles/footer/gradient/funAndTrendy";
import GradientFooter from "@/components/sections/layouts/footer/GradientFooter";

function FunAndTrendyGradientFooterContent() {
  const searchParams = useSearchParams();
  const theme = (Number(searchParams.get("theme")) || 1) as ColorTemplate;
  const style = getFunAndTrendyFooterStyle(theme);

  return <GradientFooter style={style} />;
}

export default function FunAndTrendyGradientFooterPage() {
  return (
    <ReactLenis root>
      <PageNav position="bottom" />
      <Suspense fallback={<div>Loading...</div>}>
        <FunAndTrendyGradientFooterContent />
      </Suspense>
    </ReactLenis>
  );
}