"use client";

import React, { Suspense } from "react";
import { ReactLenis } from "lenis/react";
import { PageNav } from "@/components/common/PageNav";
import { useSearchParams } from "next/navigation";
import { ColorTemplate } from "@/components/sections/styles/shared/themeConfig";
import { getFuturisticFooterStyle } from "@/components/sections/styles/footer/reveal/futuristicAndOutOfBox";
import RevealFooter from "@/components/sections/layouts/footer/RevealFooter";

function FuturisticRevealFooterContent() {
  const searchParams = useSearchParams();
  const theme = (Number(searchParams.get("theme")) || 1) as ColorTemplate;
  const style = getFuturisticFooterStyle(theme);

  return <RevealFooter style={style} />;
}

export default function FuturisticRevealFooterPage() {
  return (
    <ReactLenis root>
      <PageNav position="bottom" />
      <Suspense fallback={<div>Loading...</div>}>
        <FuturisticRevealFooterContent />
      </Suspense>
    </ReactLenis>
  );
}