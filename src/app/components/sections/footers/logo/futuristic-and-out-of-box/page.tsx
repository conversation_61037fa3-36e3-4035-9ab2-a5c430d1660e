"use client";

import React, { Suspense } from "react";
import { React<PERSON>enis } from "lenis/react";
import { PageNav } from "@/components/common/PageNav";
import { useSearchParams } from "next/navigation";
import { ColorTemplate } from "@/components/sections/styles/shared/themeConfig";
import { getFuturisticFooterStyle } from "@/components/sections/styles/footer/logo/futuristicAndOutOfBox";
import LogoFooter from "@/components/sections/layouts/footer/LogoFooter";

function FuturisticLogoFooterContent() {
  const searchParams = useSearchParams();
  const theme = (Number(searchParams.get("theme")) || 1) as ColorTemplate;
  const style = getFuturisticFooterStyle(theme);

  return <LogoFooter style={style} />;
}

export default function FuturisticLogoFooterPage() {
  return (
    <ReactLenis root>
      <PageNav position="bottom" />
      <Suspense fallback={<div>Loading...</div>}>
        <FuturisticLogoFooterContent />
      </Suspense>
    </ReactLenis>
  );
}