"use client";

import React, { Suspense } from "react";
import { ReactLenis } from "lenis/react";
import { PageNav } from "@/components/common/PageNav";
import { useSearchParams } from "next/navigation";
import { ColorTemplate } from "@/components/sections/styles/shared/themeConfig";
import { getFunAndTrendyFooterStyle } from "@/components/sections/styles/footer/logo/funAndTrendy";
import LogoFooter from "@/components/sections/layouts/footer/LogoFooter";

function FunAndTrendyLogoFooterContent() {
  const searchParams = useSearchParams();
  const theme = (Number(searchParams.get("theme")) || 1) as ColorTemplate;
  const style = getFunAndTrendyFooterStyle(theme);

  return <LogoFooter style={style} />;
}

export default function FunAndTrendyLogoFooterPage() {
  return (
    <ReactLenis root>
      <PageNav position="bottom" />
      <Suspense fallback={<div>Loading...</div>}>
        <FunAndTrendyLogoFooterContent />
      </Suspense>
    </ReactLenis>
  );
}