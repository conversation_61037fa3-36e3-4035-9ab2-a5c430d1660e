"use client";

import React, { Suspense } from "react";
import { ReactLenis } from "lenis/react";
import { PageNav } from "@/components/common/PageNav";
import { useSearchParams } from "next/navigation";
import { ColorTemplate } from "@/components/sections/styles/shared/themeConfig";
import { getFunAndTrendyFooterStyle } from "@/components/sections/styles/footer/simple/funAndTrendy";
import SimpleFooter from "@/components/sections/layouts/footer/SimpleFooter";

function FunAndTrendySimpleFooterContent() {
  const searchParams = useSearchParams();
  const theme = (Number(searchParams.get("theme")) || 1) as ColorTemplate;
  const style = getFunAndTrendyFooterStyle(theme);

  return <SimpleFooter style={style} />;
}

export default function FunAndTrendySimpleFooterPage() {
  return (
    <ReactLenis root>
      <PageNav position="bottom" />
      <Suspense fallback={<div>Loading...</div>}>
        <FunAndTrendySimpleFooterContent />
      </Suspense>
    </ReactLenis>
  );
}