'use client'

import React, { Suspense } from 'react'
import { React<PERSON>enis } from 'lenis/react'
import { PageNav } from '@/components/common/PageNav'
import { useSearchParams } from 'next/navigation'
import { ColorTemplate } from '@/components/sections/styles/shared/themeConfig'
import { getFunAndTrendyImageFAQStyle } from '@/components/sections/styles/faq/image/funAndTrendy'
import ImageFAQ from '@/components/sections/layouts/faq/ImageFAQ'

const faqItems = [
  {
    title: "What makes our platform unique?",
    content:
      "Our platform combines cutting-edge technology with user-centric design to deliver an unparalleled experience. We focus on innovation, security, and scalability to ensure our users get the best possible service. Our team of experts continuously works to improve and enhance the platform based on user feedback and industry best practices.",
  },
  {
    title: "How do I get started?",
    content:
      "Getting started is simple! First, create your account by clicking the sign-up button. Then, complete the verification process which takes just a few minutes. Once verified, you can explore all features through our guided onboarding tour. Our support team is available 24/7 to help you with any questions during the setup process.",
  },
  {
    title: "What are the pricing options?",
    content:
      "We offer flexible pricing plans to suit different needs and budgets. Our basic plan is free and includes essential features. Premium plans start at $9.99/month with advanced features and priority support. Enterprise solutions are available with custom pricing based on your specific requirements. All plans include a 30-day money-back guarantee.",
  },
  {
    title: "Is my data secure?",
    content:
      "Absolutely! We take data security very seriously. All data is encrypted both in transit and at rest using industry-standard AES-256 encryption. We comply with GDPR, SOC 2, and other major security frameworks. Regular security audits are conducted by third-party experts, and we maintain 99.9% uptime with robust backup systems.",
  },
  {
    title: "Can I integrate with other tools?",
    content:
      "Yes! Our platform offers extensive integration capabilities with over 100+ popular tools and services. We provide REST APIs, webhooks, and pre-built connectors for major platforms like Slack, Google Workspace, Microsoft 365, and many more. Our developer documentation includes detailed guides and code examples to help you get started quickly.",
  },
  {
    title: "What kind of support do you offer?",
    content:
      "We provide comprehensive support through multiple channels. Free users get access to our knowledge base and community forums. Premium users enjoy priority email support with 24-hour response times. Enterprise customers receive dedicated account managers and phone support. We also offer live chat during business hours and regular webinars for training and updates.",
  },
];

function FunAndTrendyImageFAQContent() {
  const searchParams = useSearchParams()
  const theme = (Number(searchParams.get('theme')) || 1) as ColorTemplate
  const style = getFunAndTrendyImageFAQStyle(theme)
  
  return <ImageFAQ style={style} items={faqItems} />
}

export default function FunAndTrendyImageFAQPage() {
  return (
    <ReactLenis root>
      <PageNav position="bottom" />
      <Suspense fallback={<div>Loading...</div>}>
        <FunAndTrendyImageFAQContent />
      </Suspense>
    </ReactLenis>
  )
}
