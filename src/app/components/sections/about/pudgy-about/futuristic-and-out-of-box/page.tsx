"use client";

import React, { Suspense } from "react";
import { ReactLenis } from "lenis/react";
import { PageNav } from "@/components/common/PageNav";
import { getFuturisticAboutPudgyStyle } from "@/components/sections/styles/about/pudgy/futuristicAndOutOfBox";
import PudgyAbout from "@/components/sections/layouts/about/PudgyAbout";
import { useSearchParams } from "next/navigation";
import { ColorTemplate } from "@/components/sections/styles/shared/themeConfig";

function FuturisticAboutPage() {
  const searchParams = useSearchParams();
  const theme = (searchParams.get("theme") as unknown as ColorTemplate) || 1;
  const aboutStyle = getFuturisticAboutPudgyStyle(
    Number(theme) as ColorTemplate
  );

  return (
    <ReactLenis root>
      <PageNav position="bottom" />
      <PudgyAbout style={aboutStyle} />
    </ReactLenis>
  );
}

export default function FunAndTrendyAboutPage() {
  return (
    <Suspense>
      <FuturisticAboutPage />
    </Suspense>
  );
}
