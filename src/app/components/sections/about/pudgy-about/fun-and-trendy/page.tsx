"use client";

import React, { Suspense } from "react";
import { ReactLenis } from "lenis/react";
import { PageNav } from "@/components/common/PageNav";
import { getFunAndTrendyAboutPudgyStyle } from "@/components/sections/styles/about/pudgy/funAndTrendy";
import PudgyAbout from "@/components/sections/layouts/about/PudgyAbout";
import { useSearchParams } from "next/navigation";
import { ColorTemplate } from "@/components/sections/styles/shared/themeConfig";

function FunAndTrendyAboutContent() {
  const searchParams = useSearchParams();
  const theme = (searchParams.get("theme") as unknown as ColorTemplate) || 1;
  const aboutStyle = getFunAndTrendyAboutPudgyStyle(
    Number(theme) as ColorTemplate
  );

  return (
    <ReactLenis root>
      <PageNav position="bottom" />
      <PudgyAbout style={aboutStyle} />
    </ReactLenis>
  );
}

export default function FunAndTrendyAboutPage() {
  return (
    <Suspense>
      <FunAndTrendyAboutContent />
    </Suspense>
  );
}
