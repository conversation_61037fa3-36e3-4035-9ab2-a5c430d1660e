'use client';

import React from 'react';
import Link from 'next/link';
import { PageNav } from '@/components/common/PageNav';

const sections: Array<{ name: string; href: string }> = [
  {
    name: 'Templates',
    href: '/components/sections/templates'
  },
  {
    name: 'About',
    href: '/components/sections/about'
  },
  {
    name: 'FAQ',
    href: '/components/sections/faq'
  },
  {
    name: 'Footer',
    href: '/components/sections/footers'
  },
  {
    name: 'Hero',
    href: '/components/sections/hero'
  },
  {
    name: 'How to Buy',
    href: '/components/sections/how-to-buy'
  },
  {
    name: 'Tokenomics',
    href: '/components/sections/tokenomics'
  }
];

export default function SectionsPage() {
  return (
    <section className="min-h-screen py-[var(--width-10)]">
      <PageNav />
      <div className="w-full px-[var(--width-10)]">
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
          {sections.map((section) => (
            <Link key={section.name} href={section.href}>
              <div className="aspect-square card relative rounded p-6 flex justify-center items-center text-center cursor-pointer">
                <h3 className="text-xl">{section.name}</h3>
              </div>
            </Link>
          ))}
        </div>
      </div>
    </section>
  );
}