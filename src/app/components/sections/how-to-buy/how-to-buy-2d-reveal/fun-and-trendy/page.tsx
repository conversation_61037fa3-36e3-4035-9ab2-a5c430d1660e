"use client";

import React, { Suspense } from "react";
import { ReactLenis } from "lenis/react";
import HowToBuy2D from "@/components/sections/layouts/howtobuy/HowToBuy2D";
import { PageNav } from "@/components/common/PageNav";
import { getFunAndTrendyHowToBuyStyle2D } from "@/components/sections/styles/howtobuy/how-to-buy-2d-reveal/funAndTrendy";
import { useSearchParams } from "next/navigation";
import { ColorTemplate } from "@/components/sections/styles/shared/themeConfig";

function FunAndTrendyContent() {
  const searchParams = useSearchParams();
  const theme = (Number(searchParams.get("theme")) || 1) as ColorTemplate;
  const style = getFunAndTrendyHowToBuyStyle2D(theme);

  return (
    <ReactLenis root>
      <PageNav position="bottom" />
      <HowToBuy2D style={style} />
    </ReactLenis>
  );
}

export default function FunAndTrendyHowToBuy2DPage() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <FunAndTrendyContent />
    </Suspense>
  );
}
