"use client";

import React, { Suspense } from "react";
import { React<PERSON>enis } from "lenis/react";
import HowToBuy2D from "@/components/sections/layouts/howtobuy/HowToBuy2D";
import { PageNav } from "@/components/common/PageNav";
import { getFuturisticHowToBuyStyle2D } from "@/components/sections/styles/howtobuy/how-to-buy-2d-reveal/futuristicAndOutOfBox";
import { useSearchParams } from "next/navigation";
import { ColorTemplate } from "@/components/sections/styles/shared/themeConfig";

function FuturisticContent() {
  const searchParams = useSearchParams();
  const theme = (Number(searchParams.get("theme")) || 1) as ColorTemplate;
  const style = getFuturisticHowToBuyStyle2D(theme);

  return (
    <ReactLenis root>
      <PageNav position="bottom" />
      <HowToBuy2D style={style} />
    </ReactLenis>
  );
}

export default function FuturisticAndOutOfBoxHowToBuy2DPage() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <FuturisticContent />
    </Suspense>
  );
}
