"use client";

import React, { Suspense } from "react";
import { ReactLenis } from "lenis/react";
import HowToBuy2D from "@/components/sections/layouts/howtobuy/HowToBuy2D";
import { PageNav } from "@/components/common/PageNav";
import { getFunAndTrendyMinimalHowToBuyStyle2D } from "@/components/sections/styles/howtobuy/how-to-buy-2d-simple/funAndTrendy";
import { useSearchParams } from "next/navigation";
import { ColorTemplate } from "@/components/sections/styles/shared/themeConfig";

function FunAndTrendyMinimalContent() {
  const searchParams = useSearchParams();
  const theme = (Number(searchParams.get("theme")) || 1) as ColorTemplate;
  const style = getFunAndTrendyMinimalHowToBuyStyle2D(theme);

  return (
    <ReactLenis root>
      <PageNav position="bottom" />
      <HowToBuy2D style={style} />
    </ReactLenis>
  );
}

export default function FunAndTrendyMinimalHowToBuy2DPage() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <FunAndTrendyMinimalContent />
    </Suspense>
  );
}
