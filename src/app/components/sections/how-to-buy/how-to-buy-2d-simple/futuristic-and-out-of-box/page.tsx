"use client";

import React, { Suspense } from "react";
import { ReactLenis } from "lenis/react";
import HowToBuy2D from "@/components/sections/layouts/howtobuy/HowToBuy2D";
import { PageNav } from "@/components/common/PageNav";
import { getFuturisticMinimalHowToBuyStyle2D } from "@/components/sections/styles/howtobuy/how-to-buy-2d-simple/futuristicAndOutOfBox";
import { useSearchParams } from "next/navigation";
import { ColorTemplate } from "@/components/sections/styles/shared/themeConfig";

function FuturisticMinimalContent() {
  const searchParams = useSearchParams();
  const theme = (Number(searchParams.get("theme")) || 1) as ColorTemplate;
  const style = getFuturisticMinimalHowToBuyStyle2D(theme);

  return (
    <ReactLenis root>
      <PageNav position="bottom" />
      <HowToBuy2D style={style} />
    </ReactLenis>
  );
}

export default function FuturisticMinimalHowToBuy2DPage() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <FuturisticMinimalContent />
    </Suspense>
  );
}
