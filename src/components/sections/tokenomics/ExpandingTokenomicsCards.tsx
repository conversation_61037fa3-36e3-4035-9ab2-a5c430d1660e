"use client";

import { useEffect, useRef, useState, memo } from "react";
import gsap from "gsap";
import SplitText from "gsap/SplitText";
import ScrollTrigger from "gsap/ScrollTrigger";
import { TokenomicsStyle } from "@/components/sections/styles/tokenomics/types";
import { cls } from "@/lib/utils";
import { Plus } from "lucide-react";
import PushableButton from "@/components/buttons/PushableButton";
import { useResponsive } from "@/hooks/useResponsive";
import "./ExpandingTokenomicsCards.css";

gsap.registerPlugin(SplitText, ScrollTrigger);

interface ExpandingTokenomicsCardsProps {
  style: TokenomicsStyle;
  title?: string;
  description?: string;
  cardItems?: Array<{
    id: number;
    number: string;
    title: string;
    description: string;
  }>;
}

const ExpandingTokenomicsCards = memo(function ExpandingTokenomicsCards({
  style,
  cardItems = [
    {
      id: 1,
      number: "01",
      title: "1200",
      description:
        "We are committed to acting with honesty and integrity in everything we do. Our decisions are guided by transparency, accountability, and a clear focus on doing what's right.",
    },
    {
      id: 2,
      number: "02",
      title: "393",
      description:
        "We believe in giving back to the community and making a positive impact on society through charitable initiatives and social responsibility programs.",
    },
    {
      id: 3,
      number: "03",
      title: "700",
      description:
        "We focus on sustainable economic growth and creating value for all stakeholders while maintaining financial responsibility and long-term viability.",
    },
    {
      id: 4,
      number: "04",
      title: "1200+",
      description:
        "We prioritize human-centered design and development, ensuring that our solutions serve real human needs and improve quality of life for everyone.",
    },
  ],
}: ExpandingTokenomicsCardsProps) {
  const cardTitleRef = useRef<HTMLHeadingElement>(null);
  const cardDescriptionRef = useRef<HTMLParagraphElement>(null);
  const cardContainerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (
      !cardTitleRef.current ||
      !cardDescriptionRef.current ||
      !cardContainerRef.current
    )
      return;

    const ctx = gsap.context(() => {
      const cssVarsFrom: Record<string, string> = {
        "--i": "-200%",
        "--f": "0%",
      };
      const cssVarsTo: Record<string, string> = {
        "--i": "200%",
        "--f": "200%",
      };

      const setMaskNone = (el: HTMLElement | null) => {
        if (!el) return;
        const s = el.style as CSSStyleDeclaration & {
          webkitMaskImage?: string;
          maskImage?: string;
        };
        s.webkitMaskImage = "none";
        s.maskImage = "none";
      };

      const cardTitleSplit = new SplitText(cardTitleRef.current!, {
        type: "words,lines",
      });

      gsap.fromTo(
        cardTitleSplit.words,
        { yPercent: 50, opacity: 0 },
        {
          yPercent: 0,
          opacity: 1,
          stagger: 0.05,
          duration: 0.75,
          ease: "power1",
          scrollTrigger: { trigger: cardTitleRef.current, start: "top 95%" },
          onComplete: () => setMaskNone(cardTitleRef.current),
        }
      );

      gsap.fromTo(cardTitleRef.current, cssVarsFrom, {
        ...cssVarsTo,
        duration: 2,
        ease: "power2",
        scrollTrigger: { trigger: cardTitleRef.current, start: "top 95%" },
      });

      gsap.fromTo(cardDescriptionRef.current, cssVarsFrom, {
        ...cssVarsTo,
        duration: 2,
        delay: 0.2,
        ease: "power2",
        scrollTrigger: {
          trigger: cardDescriptionRef.current,
          start: "top 95%",
        },
        onComplete: () => setMaskNone(cardDescriptionRef.current),
      });

      gsap.fromTo(cardContainerRef.current, cssVarsFrom, {
        ...cssVarsTo,
        duration: 2,
        ease: "power2",
        scrollTrigger: { trigger: cardContainerRef.current, start: "top 95%" },
        onComplete: () => setMaskNone(cardContainerRef.current),
      });
    });

    return () => {
      ctx.revert();
    };
  }, []);

  const [expandedIndex, setExpandedIndex] = useState<number>(0);
  const { isMobile } = useResponsive({ breakpoint: 768 });

  const toggleCard = (index: number) => {
    setExpandedIndex((prev) => (prev === index ? -1 : index));
  };

  return (
    <section className="h-fit w-full">
      <div
        className={cls(
          "w-full flex md:gap-4",
          isMobile ? "flex-col" : "flex-wrap md:flex-nowrap"
        )}
        ref={cardContainerRef}
      >
        {cardItems.map((item, index) => {
          const isExpanded = expandedIndex === index;
          const expandingCards = style.expandingCards;

          let cardClassName =
            expandingCards?.cardClassName ||
            "relative w-full flex flex-col rounded overflow-hidden transition-all duration-700 ease-[cubic-bezier(0.4,0,0.2,1)] cursor-pointer select-none";

          // Add mobile-specific or desktop-specific classes
          if (isMobile) {
            cardClassName += " p-6 mb-4 expanding-card-mobile";
            // Mobile accordion-style height
            cardClassName += isExpanded
              ? " min-h-[200px] expanded"
              : " h-[80px] collapsed";
          } else {
            cardClassName += " md:h-90 p-8 md:p-10";
            // Desktop expanding width
            cardClassName += isExpanded ? " md:w-[26vw]" : " md:w-[17vw]";
          }

          if (isExpanded) {
            cardClassName += ` ${
              expandingCards?.activeCardClassName || "opacity-100"
            }`;
          } else {
            cardClassName += ` ${
              expandingCards?.inactiveCardClassName ||
              "opacity-60 hover:opacity-90"
            }`;
          }

          const renderButton = () => {
            const buttonType = expandingCards?.buttonType || "plus";
            const buttonClassName =
              expandingCards?.buttonClassName || "w-8 h-8 md:w-10 md:h-10";
            const buttonContentClassName =
              expandingCards?.buttonContentClassName || "";
            const buttonIconClassName =
              expandingCards?.buttonIconClassName || "w-4 h-4 md:w-5 md:h-5";

            if (buttonType === "pushable") {
              return (
                <PushableButton
                  variant="side"
                  className={buttonClassName}
                  frontClassName={buttonContentClassName}
                >
                  <Plus
                    className={cls(
                      buttonIconClassName,
                      "transition-transform duration-300",
                      isExpanded ? "rotate-45" : "rotate-0"
                    )}
                    strokeWidth={2}
                  />
                </PushableButton>
              );
            } else if (buttonType === "slide") {
              return (
                <button type="button" className={buttonClassName}>
                  <Plus
                    className={cls(
                      buttonIconClassName,
                      "transition-transform duration-300",
                      isExpanded ? "rotate-45" : "rotate-0"
                    )}
                    strokeWidth={2}
                  />
                </button>
              );
            } else {
              return (
                <div
                  className={cls(
                    "rounded-full bg-black flex items-center justify-center",
                    buttonClassName
                  )}
                >
                  <Plus
                    className={cls(
                      buttonIconClassName,
                      "text-white transition-transform duration-300",
                      isExpanded ? "rotate-45" : "rotate-0"
                    )}
                    strokeWidth={2}
                  />
                </div>
              );
            }
          };

          return (
            <div
              key={item.id}
              className={cardClassName}
              onClick={() => toggleCard(index)}
            >
              {/* Main content area */}
              <div className="flex flex-col h-full relative">
                {/* Mobile layout */}
                {isMobile ? (
                  <>
                    {/* Collapsed state - show title and button */}
                    <div
                      className={cls(
                        "flex items-center justify-between",
                        isExpanded ? "mb-4" : "h-full"
                      )}
                    >
                      <h3
                        className={cls(
                          expandingCards?.titleClassName,
                          isExpanded && "text-white"
                        )}
                      >
                        {item.title}
                      </h3>
                      <div>{renderButton()}</div>
                    </div>

                    {/* Expanded content */}
                    {isExpanded && (
                      <div className="flex-1 overflow-hidden">
                        <p
                          className={cls(
                            expandingCards?.descriptionClassName,
                            "text-white transition-all duration-500 ease-in-out"
                          )}
                        >
                          {item.description}
                        </p>
                      </div>
                    )}
                  </>
                ) : (
                  /* Desktop layout */
                  <>
                    {/* Top section: Number and Title */}
                    <div className="flex flex-col gap-2 mb-4">
                      <h3
                        className={cls(
                          expandingCards?.titleClassName,
                          isExpanded && "text-white"
                        )}
                      >
                        {item.title}
                      </h3>
                    </div>

                    {/* Description - shows when expanded */}
                    <div className="flex-1 mb-16">
                      <div
                        className={cls(
                          "transition-opacity duration-500 ease-in-out",
                          isExpanded ? "opacity-100" : "opacity-0"
                        )}
                      >
                        <p
                          className={cls(
                            expandingCards?.descriptionClassName,
                            isExpanded && "text-white"
                          )}
                        >
                          {item.description}
                        </p>
                      </div>
                    </div>

                    {/* Button - fixed at bottom left */}
                    <div className="absolute -bottom-3 left-0">
                      {renderButton()}
                    </div>
                  </>
                )}
              </div>
            </div>
          );
        })}
      </div>
    </section>
  );
});

ExpandingTokenomicsCards.displayName = "ExpandingTokenomicsCards";

export default ExpandingTokenomicsCards;
