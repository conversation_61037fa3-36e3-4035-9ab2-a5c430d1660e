import React, { memo } from "react";
import ImageAccordion from "@/components/accordions/ImageAccordion";
import TextRenderer from "@/components/sections/layouts/TextRenderer";
import { ImageFAQStyle, AccordionItem } from "../../styles/faq/types";
import { cls } from "@/lib/utils";

interface ImageFAQProps {
  style: ImageFAQStyle;
  items: AccordionItem[];
}

const ImageFAQ = memo(function ImageFAQ({ style, items }: ImageFAQProps) {
  return (
    <section
      className={cls(
        "min-h-screen flex items-center justify-center",
        style.section.className,
        style.section.backgroundColor
      )}
    >
      {/* Gradient Overlay */}
      {style.gradient?.show && (
        <div
          className={cls(
            "absolute pointer-events-none",
            style.gradient.inset || "inset-0",
            style.gradient.rounded || "rounded-none",
            style.gradient.className
          )}
          style={{
            background: `
                            radial-gradient(circle at center, ${
                              style.gradient.radialColor || "transparent"
                            } ${
              style.gradient.radialOpacity || "0%"
            }, transparent 70%),
                            linear-gradient(180deg, transparent 0%, ${
                              style.gradient.linearColor || "transparent"
                            } ${style.gradient.linearOpacity || "0%"})
                        `,
          }}
        />
      )}

      <div
        className={cls("relative z-10 w-full", style.section.innerClassName)}
      >
        <div className="text-center mb-8">
          <TextRenderer config={style.title} as="h1" />

          {style.description && (
            <p className={style.description.className}>
              {style.description.text}
            </p>
          )}
        </div>

        {/* FAQ Accordion with Image */}
        <ImageAccordion
          items={items}
          imageSrc={style.image.src}
          imageAlt={style.image.alt || "FAQ Image"}
          className={style.accordion.className}
          titleClassName="hidden"
          imageContainerClassName={style.image.containerClassName}
          imageClassName={style.image.className}
          accordionContainerClassName={style.accordion.containerClassName}
          itemClassName={style.accordion.itemClassName}
          itemTitleClassName={style.accordion.itemTitleClassName}
          itemIconContainerClassName={
            style.accordion.itemIconContainerClassName
          }
          itemIconClassName={style.accordion.itemIconClassName}
          itemContentClassName={style.accordion.itemContentClassName}
        />
      </div>
    </section>
  );
});

export default ImageFAQ;
