"use client";

import { memo } from "react";
import { cls } from "@/lib/utils";
import TextRenderer from "@/components/sections/layouts/TextRenderer";
import { AboutStyle } from "@/components/sections/styles/about/types";
import Image from "next/image";
import VerticalTextbox from "@/components/textbox/Verticaltextbox";
import SideGlowGradientBackground from "@/components/background/SideGlowGradientBackground";
import PushableButton from "@/components/buttons/PushableButton";

interface PudgyAboutProps {
  style: AboutStyle;
  title?: string;
  descriptions?: string[];
}

const PudgyAbout = ({
  style,
  title = "PENGU COIN",
  descriptions = [
    "The Pudgy Penguins brand produces content, merchandise, toys, and digital collectables. We believe in the power of play and imagination, and we're committed to helping you unlock your inner child.",
  ],
}: PudgyAboutProps) => {
  return (
    <section
      className={cls(
        "w-full px-[var(--width-5)] py-10",
        style.section.secondaryBg
      )}
    >
      <div
        className={cls(
          "relative mx-auto",
          style.section.backgroundColor,
          style.section.className,
          style.section.fadeBottom
            ? "[mask-image:linear-gradient(to_bottom,black_0%,black_90%,transparent_100%)]"
            : undefined
        )}
      >
        {style.gradient?.show && (
          <SideGlowGradientBackground
            inset={style.gradient.inset}
            rounded={style.gradient.rounded}
            radialColor={style.gradient.radialColor}
            linearColor={style.gradient.linearColor}
            radialOpacity={style.gradient.radialOpacity}
            linearOpacity={style.gradient.linearOpacity}
            linearOpacityMobile={style.gradient.linearOpacityMobile}
          />
        )}
        {style.section.backgroundPattern && (
          <div
            className={cls(
              "absolute inset-0 opacity-20",
              style.section.backgroundPattern,
              "bg-repeat"
            )}
          />
        )}
        <div className="max-w-[var(--width-90)] px-[var(--vw-2)] md:px-12 mx-auto relative z-10 grid md:grid-cols-2 gap-8 md:gap-10">
          <div className="flex flex-col justify-between items-start gap-4">
            <VerticalTextbox
              title={
                <TextRenderer
                  config={{ ...style.title, text: title }}
                  as="h2"
                />
              }
              description={
                <div className={style.descriptions.containerClassName}>
                  {descriptions.map((description, idx) => (
                    <p key={idx} className={style.descriptions.className}>
                      {description}
                    </p>
                  ))}
                </div>
              }
              className={style.layout.textboxClassName}
              alignStart={true}
              titleClassName="md:!w-full"
              descriptionClassName={style.layout.descriptionClassName}
            />
            <div className="flex gap-6 mt-2 md:mt-0">
              <PushableButton
                type="button"
                variant={style.button?.variant || "side"}
                className={style.button?.className}
                frontClassName={style.button?.childClassName}
              >
                JOIN NOW
              </PushableButton>
            </div>
          </div>
          <div className={style.image?.parentClassName}>
            <Image
              src={"/sections/images/character3.webp"}
              alt="pudgy penguins about"
              width={600}
              height={500}
              className={style.image?.className}
            />
          </div>
          {style.section.showBorder && (
            <div className="w-full h-px bg-white/10 mt-20 md:mt-30" />
          )}
        </div>
      </div>
    </section>
  );
};

PudgyAbout.displayName = "PudgyAbout";

export default memo(PudgyAbout);
