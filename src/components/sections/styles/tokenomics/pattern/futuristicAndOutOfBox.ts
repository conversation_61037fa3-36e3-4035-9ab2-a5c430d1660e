import { TokenomicsStyle } from "../types";
import { futuristicTheme as theme } from "../../shared/themes";
import { ColorTemplate } from "../../shared/themeConfig";
import { getFuturisticColors } from "../../shared/themeConfig";

export function getFuturisticTokenomicsStyle(
  colorTemplate: ColorTemplate = 1
): TokenomicsStyle {
  const colors = getFuturisticColors(colorTemplate);

  return {
    section: {
      className: theme.spacing.sectionPadding,
      backgroundColor: colors.primary,
      spotlight: {
        width: "25%",
        height: "150%",
        left: "0%",
        top: "-30%",
        rotate: "-60deg",
        color: colors.spotlight,
        blur: "100px",
        opacity: 1,
        mobileWidth: "55%",
        mobileHeight: "70%",
        mobileLeft: "-10%",
        mobileTop: "-60%",
        mobileRotate: "-30deg",
      },
    },
    title: {
      className: `text-6xl md:text-8xl leading-none tracking-tight ${theme.text.headingClass} ${theme.fonts.heading.className}`,
      animation: "slide",
      useRetroText: false,
      animationProps: {
        duration: theme.animations.duration,
        stagger: theme.animations.stagger,
        start: "top 80%",
        end: "top 20%",
        variant: theme.animations.variant,
      },
      gradientColors: theme.gradients.text,
    },
    description: {
      className: `mt-4 text-lg font-medium max-w-3xl ${theme.fonts.body.className} text-off-white`,
    },
    bento: {
      button: {
        className: `aspect-square flex items-center justify-center cursor-pointer inset-shadow-2xs inset-shadow-blue/30 ${colors.cardBg} backdrop-blur-3xl futuristic-card-border z-10`,
        childClassName:
          "bg-transparent !px-0 h-12 md:h-15 w-auto aspect-square",
        iconClassName: "h-[35%] w-auto text-white",
        variant: "none" as const,
      },
      items: [],
      className: "px-0!",
      itemClassName: `${colors.cardBg} futuristic-card-border h-90!`,
      valueClassName: `text-white! font-semibold ${theme.fonts.heading.className}`,
      descriptionClassName: `font-medium ${theme.fonts.body.className} text-off-white`,
      gradientClassName: colors.patternGradient,
    },
  };
}

export const futuristicTokenomicsStyle = getFuturisticTokenomicsStyle(1);
