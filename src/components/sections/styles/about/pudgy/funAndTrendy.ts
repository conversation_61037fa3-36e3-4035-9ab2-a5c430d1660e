import { AboutStyle } from "../types";
import { funAndTrendyTheme as theme } from "../../shared/themes";
import { ColorTemplate } from "../../shared/themeConfig";
import { getFunAndTrendyColors } from "../../shared/themeConfig";

export function getFunAndTrendyAboutPudgyStyle(
  colorTemplate: ColorTemplate = 1
): AboutStyle {
  const colors = getFunAndTrendyColors(colorTemplate);
  return {
    section: {
      className: `${theme.spacing.sectionPadding} border-4  rounded-2xl`,
      secondaryBg: colors.secondary,
      backgroundColor: colors.primary,
      backgroundPattern: theme.backgrounds.texture,
    },
    title: {
      className: `${theme.heading.sizes.hero} lg:!text-[clamp(4.75rem,8vw,8.5rem)] leading-none uppercase tracking-tight mb-6 ${theme.text.headingClass} ${theme.text.white} ${theme.heading.className}`,
      shadowOffset: theme.shadows.retro.offset,
      useRetroText: true,
      animation: "slide",
      shadowColor: theme.shadows.retro.color,
      animationProps: {
        duration: theme.animations.duration,
        stagger: theme.animations.stagger,
        start: "top 80%",
        end: "top 20%",
        variant: theme.animations.variant,
      },
    },
    descriptions: {
      className: `${theme.text.white} md:!text-2xl ${theme.description.className}`,
      containerClassName: theme.spacing.containerGap,
    },
    layout: {
      alignStart: theme.layout.alignStart,
      descriptionClassName: "w-full",
      textboxClassName: "!gap-3 md:!gap-6",
    },
    button: {
      className: " bg-blue ring-2 ring-inset ring-black",
      childClassName:
        "h-13 md:!h-15 w-auto bg-white ring-2 ring-inset ring-black text-black font-bold tracking-wide text-base md:text-xl",
    },
    image: {
      parentClassName: "w-full mx-auto md:h-[60vh]",
      className: "rounded-xl object-contain h-full w-full ",
    },
  };
}

export const funAndTrendyPudgyAboutStyle = getFunAndTrendyAboutPudgyStyle(1);
