import { FooterStyle } from "../types";
import { futuristicTheme as theme } from "../../shared/themes";
import { ColorTemplate } from "../../shared/themeConfig";
import { getFuturisticColors } from "../../shared/themeConfig";

export function getFuturisticFooterStyle(
  colorTemplate: ColorTemplate = 1
): FooterStyle {
  const colors = getFuturisticColors(colorTemplate);

  return {
    columnTitleClassName: `${theme.text.muted} ${theme.fonts.body.className}`,
    columns: [
      {
        title: "Company",
        items: [
          { label: "About", onClick: () => console.log("About clicked") },
          { label: "Blog", onClick: () => console.log("Blog clicked") },
          { label: "Careers", onClick: () => console.log("Careers clicked") },
          { label: "Contact", onClick: () => console.log("Contact clicked") },
        ],
      },
      {
        title: "Resources",
        items: [
          {
            label: "Community",
            onClick: () => console.log("Community clicked"),
          },
          { label: "Support", onClick: () => console.log("Support clicked") },
          { label: "Status", onClick: () => console.log("Status clicked") },
          { label: "Partners", onClick: () => console.log("Partners clicked") },
        ],
      },
      {
        title: "Legal",
        items: [
          { label: "Terms", onClick: () => console.log("Terms clicked") },
          { label: "Privacy", onClick: () => console.log("Privacy clicked") },
          { label: "Cookies", onClick: () => console.log("Cookies clicked") },
          { label: "License", onClick: () => console.log("License clicked") },
        ],
      },
    ],
    logoSrc: "/images/logowhite.svg",
    logoWidth: 120,
    logoHeight: 40,
    copyrightText: "© 2025 | Webild",
    onPrivacyClick: () => console.log("Privacy Policy clicked"),
    logoAlt: "Webild Logo",
    logoText: "Webild",
    className: colors.primary,
    copyrightContainerClassName: `border-white/20`,
    privacyButtonClassName: `${theme.text.muted} ${theme.fonts.body.className}`,
    copyrightTextClassName: `${theme.text.muted} ${theme.fonts.body.className}`,
    gradientClassName: colors.gradientLinear,
    svgClassName: colors.spotlight,
    columnItemClassName: `${theme.fonts.body.className}`
  };
}

export const futuristicFooterStyle = getFuturisticFooterStyle(1);
