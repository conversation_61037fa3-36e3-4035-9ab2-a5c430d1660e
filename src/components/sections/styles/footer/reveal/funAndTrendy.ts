import { FooterStyle } from "../types";
import { funAndTrendyTheme as theme } from "../../shared/themes";
import { ColorTemplate } from "../../shared/themeConfig";
import { getFunAndTrendyColors } from "../../shared/themeConfig";

export function getFunAndTrendyFooterStyle(
  colorTemplate: ColorTemplate = 1
): FooterStyle {
  const colors = getFunAndTrendyColors(colorTemplate);

  return {
    columns: [
      {
        title: "Company",
        items: [
          { label: "About", onClick: () => console.log("About clicked") },
          { label: "Blog", onClick: () => console.log("Blog clicked") },
          { label: "Careers", onClick: () => console.log("Careers clicked") },
          { label: "Contact", onClick: () => console.log("Contact clicked") },
        ],
      },
      {
        title: "Resources",
        items: [
          {
            label: "Community",
            onClick: () => console.log("Community clicked"),
          },
          { label: "Support", onClick: () => console.log("Support clicked") },
          { label: "Status", onClick: () => console.log("Status clicked") },
          { label: "Partners", onClick: () => console.log("Partners clicked") },
        ],
      },
      {
        title: "Legal",
        items: [
          { label: "Terms", onClick: () => console.log("Terms clicked") },
          { label: "Privacy", onClick: () => console.log("Privacy clicked") },
          { label: "Cookies", onClick: () => console.log("Cookies clicked") },
          { label: "License", onClick: () => console.log("License clicked") },
        ],
      },
    ],
    logoSrc: "/images/logowhite.svg",
    logoWidth: 120,
    logoHeight: 40,
    copyrightText: "© 2025 | Webild",
    onPrivacyClick: () => console.log("Privacy Policy clicked"),
    logoAlt: "Webild Logo",
    logoText: "Webild",
    className: `${colors.secondary}`,
    columnTitleClassName: `text-white/60 ${theme.fonts.body.className}`,
    copyrightContainerClassName: `border-white/40`,
    columnItemClassName: `text-base font-bold! uppercase ${theme.fonts.body.className}`,
    privacyButtonClassName: `${theme.text.white} ${theme.fonts.body.className}`,
    copyrightTextClassName: `${theme.text.white} ${theme.fonts.body.className}`,
    gradientClassName: 'bg-black',
  };
}

export const funAndTrendyFooterStyle = getFunAndTrendyFooterStyle(1);
