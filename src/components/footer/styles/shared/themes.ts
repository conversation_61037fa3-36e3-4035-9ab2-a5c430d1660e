import { <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>_<PERSON>, Press_Start_2P,Audiowide } from 'next/font/google';


export const antonio = <PERSON>({
    weight: ['100', '200', '300', '400', '500', '600', '700'],
    subsets: ['latin'],
});

export const barlow = Barlow({
    weight: ['400', '500', '600', '700', '800', '900'],
    subsets: ['latin'],
});

export const dmSans = DM_Sans({
    weight: ['300', '400', '500', '600', '700', '800', '900'],
    subsets: ['latin'],
});

export const inter = Inter({
    weight: ['300', '400', '500', '600', '700', '800', '900'],
    subsets: ['latin'],
});

export const luckiestGuy = Luckiest_Guy({
    weight: ['400'],
    subsets: ['latin'],
});

export const pressStart2P = Press_Start_2P({
    weight: ['400'],
    subsets: ['latin'],
});

export const audiowide = Audiowide({
    weight: ['400'],
    subsets: ['latin'],
});

export const funAndTrendyTheme = {
    fonts: {
        heading: antonio,
        body: barlow
    },
    text: {
        headingClass: 'font-extrabold uppercase',
        bodyClass: 'font-bold uppercase',
        white: 'text-white'
    },
};

export const futuristicTheme = {
    fonts: {
        heading: inter,
        body: inter
    },
    text: {
        headingClass: 'font-medium leading-[1.15] tracking-tight',
        bodyClass: 'tracking-tight',
        white: 'text-white',
        muted: 'text-white/75'
    },
};