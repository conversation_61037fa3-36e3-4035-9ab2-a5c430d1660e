export type ColorTemplate = 1 | 2;

export interface ColorThemeProps {
  colorTemplate?: ColorTemplate;
}

export const funAndTrendyColors = {
  1: {
    primary: "bg-fun-template-1-primary",
    secondary: "bg-fun-template-1-secondary",
    tertiary: "bg-fun-template-1-tertiary",
    gradient: "bg-gradient-to-r from-pink-500 via-rose-600 to-red-600",
  },
  2: {
    primary: "bg-fun-template-2-primary",
    secondary: "bg-fun-template-2-secondary",
    tertiary: "bg-fun-template-2-tertiary",
    gradient: "bg-gradient-to-r from-emerald-500 via-teal-600 to-cyan-600",
  },
} as const;

export const futuristicColors = {
  1: {
    primary: "bg-futuristic-template-1-primary",
    gradientLinear: "var(--color-futuristic-template-1-gradient-linear)",
    cardBg: "futuristic-template-1-card-bg",
    spotlight: "var(--color-futuristic-template-1-spotlight)",
    gradient: "bg-gradient-to-r from-futuristic-template-1-gradient-linear via-purple-600/70 to-purple-400",
  },
  2: {
    primary: "bg-futuristic-template-2-primary",
    gradientLinear: "var(--color-futuristic-template-2-gradient-linear)",
    cardBg: "futuristic-template-2-card-bg",
    spotlight: "var(--color-futuristic-template-2-spotlight)",
    gradient: "bg-gradient-to-r from-futuristic-template-2-gradient-linear via-blue-600/70 to-blue-400",
  },
} as const;

export function getFunAndTrendyColors(template: ColorTemplate) {
  return funAndTrendyColors[template];
}

export function getFuturisticColors(template: ColorTemplate) {
  return futuristicColors[template];
}
