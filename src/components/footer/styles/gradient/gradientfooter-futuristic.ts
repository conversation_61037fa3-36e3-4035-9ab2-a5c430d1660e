import { FooterStyle } from "../shared/types";
import { futuristicTheme as theme } from "../shared/themes";
import { ColorTemplate } from "../shared/themeConfig";
import { getFuturisticColors } from "../shared/themeConfig";

export function getFuturisticgradientFooterStyle(
  colorTemplate: ColorTemplate = 1
): FooterStyle {
  const colors = getFuturisticColors(colorTemplate);

  return {
    items: [
      { label: "About", onClick: () => console.log("About clicked") },
      { label: "Services", onClick: () => console.log("Services clicked") },
      { label: "Portfolio", onClick: () => console.log("Portfolio clicked") },
      { label: "Contact", onClick: () => console.log("Contact clicked") },
      { label: "Privacy", onClick: () => console.log("Privacy clicked") },
      { label: "Terms", onClick: () => console.log("Terms clicked") },
    ],
    logoSrc: "/images/logowhite.svg",
    logoAlt: "Webild gradient",
    logoText: "Webild",
    className: `rounded-t-[var(--width-10)] md:rounded-t-[calc(var(--width-10)/2)] ${colors.primary} text-white! ${theme.fonts.body.className}`,
    gradientClassName: colors.gradient,
    svgClassName: colors.spotlight,
  };
}

export const futuristicandoutofboxStyle = getFuturisticgradientFooterStyle(1);
