import { FooterStyle } from "../shared/types";
import { funAndTrendyTheme as theme } from "../shared/themes";
import { ColorTemplate } from "../shared/themeConfig";
import { getFunAndTrendyColors } from "../shared/themeConfig";

export function getFunAndTrendygradientFooterStyle(
  colorTemplate: ColorTemplate = 1
): FooterStyle {
  const colors = getFunAndTrendyColors(colorTemplate);

  return {
    items: [
      { label: "About", onClick: () => console.log("About clicked") },
      { label: "Services", onClick: () => console.log("Services clicked") },
      { label: "Portfolio", onClick: () => console.log("Portfolio clicked") },
      { label: "Contact", onClick: () => console.log("Contact clicked") },
      { label: "Privacy", onClick: () => console.log("Privacy clicked") },
      { label: "Terms", onClick: () => console.log("Terms clicked") },
    ],
    logoSrc: "/images/logowhite.svg",
    logoAlt: "Webild Logo",
    logoText: "Webild",
    className: `${colors.secondary} border-t-4 border-t-black ${theme.fonts.body.className}`,
    buttonClassName: "text-white!"
  };
}

export const funandtrendyStyle = getFunAndTrendygradientFooterStyle(1);
