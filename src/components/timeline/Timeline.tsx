"use client";

import React, { useCallback } from "react";
import Image from "next/image";
import { RegularTimelineStyle } from "./styles/shared/types";

interface TimelineItem {
  title: string;
  description: string;
  video?: string;
  image?: string;
}

interface TimelineProps {
  items: TimelineItem[];
  style?: RegularTimelineStyle;
  title?: string;
  className?: string;
}

const Timeline = ({
  items,
  style,
  title = "Timeline",
  className = "",
}: TimelineProps) => {
  // Default style if none provided
  const defaultStyle: RegularTimelineStyle = {
    section: {
      className: "relative overflow-visible h-fit px-[var(--width-10)]",
      backgroundColor: "bg-transparent",
    },
    timeline: {
      lineColor: "bg-white/10",
      activeLineColor: "bg-white",
      itemBackground: "bg-white/20",
      itemActiveBackground: "bg-white/30",
      dotColor: "bg-white",
      dotActiveColor: "bg-white",
    },
    typography: {
      titleClassName: "text-xl font-semibold leading-[110%]",
      descriptionClassName: "text-sm leading-[110%]",
    },
    colors: {
      textPrimary: "text-black",
      textSecondary: "text-gray-600",
      border: "border-gray-200",
      shadow: "shadow",
    },
    spacing: {
      gap: "gap-[var(--width-30)] md:gap-30",
      padding: "p-3",
      margin: "",
    },
    card: {
      background: "bg-white/20 shadow backdrop-blur-sm",
      activeBackground: "bg-white/30 shadow backdrop-blur-sm",
      border: "rounded",
      shadow: "shadow",
      imageContainer: "bg-white rounded",
    },
    media: {
      borderRadius: "rounded",
      shadow: "shadow",
    },
  };

  const appliedStyle = style || defaultStyle;
  const getItemClasses = useCallback(
    (index: number) => {
      const baseClasses = `relative overflow-hidden w-60 md:w-22_5 h-fit ${appliedStyle.spacing.padding} flex flex-col gap-3 ${appliedStyle.card.background}`;
      const alignmentClass =
        index % 2 === 0 ? "self-end mr-0" : "self-start ml-0";
      const marginClasses = [
        index % 4 === 0 ? "md:mr-10" : "",
        index % 4 === 1 ? "md:ml-20" : "",
        index % 4 === 2 ? "md:mr-15" : "",
        index % 4 === 3 ? "md:ml-0" : "",
      ]
        .filter(Boolean)
        .join(" ");

      return `${baseClasses} ${alignmentClass} ${marginClasses}`;
    },
    [appliedStyle]
  );
  return (
    <section
      className={`${appliedStyle.section.className} ${appliedStyle.section.backgroundColor} ${className}`}
    >
      {appliedStyle.gradient?.show && (
        <div
          className={`absolute ${appliedStyle.gradient.inset} ${appliedStyle.gradient.rounded} bg-gradient-to-b from-transparent via-transparent to-black/20 pointer-events-none`}
          style={{
            background: `radial-gradient(ellipse at center, ${appliedStyle.gradient.radialColor} ${appliedStyle.gradient.radialOpacity}, transparent 70%), linear-gradient(to bottom, transparent 0%, ${appliedStyle.gradient.linearColor} ${appliedStyle.gradient.linearOpacity})`,
          }}
        />
      )}
      <div
        className={`relative z-10 w-full flex flex-col ${appliedStyle.spacing.gap}`}
      >
        {items.map((item, index) => (
          <div key={index} className={getItemClasses(index)}>
            <div
              className={`relative overflow-hidden w-full h-auto ${appliedStyle.media.borderRadius} flex items-center justify-center ${appliedStyle.card.imageContainer}`}
            >
              {item.video ? (
                <video
                  src={item.video}
                  className="w-full h-auto"
                  autoPlay
                  muted
                  playsInline
                  loop
                />
              ) : item.image ? (
                <Image
                  src={item.image}
                  alt={item.title}
                  width={500}
                  height={300}
                  className="w-full h-auto"
                />
              ) : (
                <div className="w-full h-48 rounded" />
              )}
            </div>
            <div className="flex flex-col gap-1 px-3 mb-3">
              <h2 className={appliedStyle.typography.titleClassName}>
                {item.title}
              </h2>
              <h3 className={appliedStyle.typography.descriptionClassName}>
                {item.description}
              </h3>
            </div>
          </div>
        ))}
      </div>
      <div className="absolute z-0 top-0 left-0 w-full h-full overflow-visible">
        <div className="sticky top-0 left-0 w-full h-screen flex items-center justify-center text-center">
          <h1 className="text-6xl font-bold">{title}</h1>
        </div>
      </div>
    </section>
  );
};

Timeline.displayName = "Timeline";

export default React.memo(Timeline);
