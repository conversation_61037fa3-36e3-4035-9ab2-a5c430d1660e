import { <PERSON>, <PERSON>, <PERSON> } from 'next/font/google';

export const antonio = <PERSON>({
    weight: ['100', '200', '300', '400', '500', '600', '700'],
    subsets: ['latin'],
});

export const barlow = Barlow({
    weight: ['100', '200', '300', '400', '500', '600', '700', '800', '900'],
    subsets: ['latin'],
});

export const inter = Inter({
    weight: ['100', '200', '300', '400', '500', '600', '700', '800', '900'],
    subsets: ['latin'],
});

export const funAndTrendyTimelineTheme = {
    fonts: {
        heading: antonio,
        body: barlow
    },
    text: {
        headingClass: 'font-extrabold uppercase',
        bodyClass: 'font-bold uppercase',
        white: 'text-white',
        black: 'text-black'
    },
    animations: {
        duration: 1,
        stagger: 0.02,
        variant: 'trigger' as const,
        scrollTrigger: {
            start: 'top 80%',
            end: 'top 20%'
        }
    },
    backgrounds: {
        texture: "bg-[url('/sections/images/funandtrendytexture.png')]",
        heroImage: "/sections/images/funandtrendyherobackground.jpeg"
    },
    spacing: {
        sectionPadding: 'py-20 md:py-30',
        gap: 'gap-4 md:gap-10',
        containerGap: 'flex flex-col gap-4 md:gap-6',
        timelineGap: 'gap-[var(--width-10)] md:gap-10'
    },
    borders: {
        button: 'border-2 border-black',
        section: 'border-y-3 border-black',
        card: 'border-2 border-black',
        timeline: 'border-2 border-black'
    },
    shadows: {
        retro: {
            offset: '0.045em',
            color: 'black'
        },
        card: 'shadow-[4px_4px_0_#000]',
        timeline: 'shadow-[4px_4px_0_#000]'
    },
    typography: {
        year: {
            className: `text-8xl leading-[100%] font-extrabold uppercase ${antonio.className}`,
            sizes: {
                large: 'text-8xl md:text-9xl',
                medium: 'text-6xl md:text-8xl',
                small: 'text-4xl md:text-6xl'
            }
        },
        title: {
            className: `text-xl md:text-2xl font-extrabold uppercase leading-[110%] ${antonio.className}`,
            sizes: {
                large: 'text-2xl md:text-3xl',
                medium: 'text-xl md:text-2xl',
                small: 'text-lg md:text-xl'
            }
        },
        description: {
            className: `text-sm md:text-base font-bold uppercase leading-[120%] ${barlow.className}`
        }
    }
};

export const futuristicTimelineTheme = {
    fonts: {
        heading: inter,
        body: inter
    },
    text: {
        headingClass: 'font-medium leading-[1.15] tracking-tight',
        bodyClass: 'tracking-tight',
        white: 'text-white',
        muted: 'text-white/75'
    },
    animations: {
        duration: 1.2,
        stagger: 0.05,
        variant: 'trigger' as const,
        scrollTrigger: {
            start: 'top 80%',
            end: 'top 20%'
        }
    },
    backgrounds: {
        gradient: {
            radialColor: '#030014',
            linearColor: '#7538c2',
            radialOpacity: '0%',
            linearOpacity: '70%'
        }
    },
    spacing: {
        sectionPadding: 'py-20 md:py-30',
        gap: 'gap-4 md:gap-6',
        containerGap: 'flex flex-col gap-4 md:gap-6',
        timelineGap: 'gap-6 md:gap-8'
    },
    borders: {
        button: '!border !border-white/10',
        card: 'border border-white/10',
        timeline: 'border-white/20'
    },
    gradients: {
        text: {
            from: '#fff',
            to: 'rgba(255,255,255,0.5)'
        }
    },
    typography: {
        year: {
            className: `text-8xl leading-[100%] font-medium tracking-tight ${inter.className}`,
            sizes: {
                large: 'text-8xl md:text-9xl',
                medium: 'text-6xl md:text-8xl',
                small: 'text-4xl md:text-6xl'
            }
        },
        title: {
            className: `text-xl md:text-2xl font-medium leading-[110%] tracking-tight ${inter.className}`,
            sizes: {
                large: 'text-2xl md:text-3xl',
                medium: 'text-xl md:text-2xl',
                small: 'text-lg md:text-xl'
            }
        },
        description: {
            className: `text-sm md:text-base tracking-tight leading-[120%] ${inter.className}`
        }
    },
    effects: {
        backdrop: 'backdrop-blur-md',
        glow: 'shadow-lg shadow-purple-500/20',
        glass: 'bg-white/5 backdrop-blur-md border border-white/10'
    }
};
