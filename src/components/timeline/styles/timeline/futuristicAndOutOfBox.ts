import { RegularTimelineStyle } from "../shared/types";
import { futuristicTimelineTheme as theme } from "../shared/themes";
import { ColorTemplate } from "../shared/themeConfig";
import { getFuturisticTimelineColors } from "../shared/themeConfig";

export function getFuturisticTimelineStyle(
  colorTemplate: ColorTemplate = 1
): RegularTimelineStyle {
  const colors = getFuturisticTimelineColors(colorTemplate);
  
  return {
    section: {
      className: `${theme.spacing.sectionPadding} relative overflow-visible px-[var(--width-10)]`,
      backgroundColor: colors.primary,
      showBorder: false,
      fadeBottom: true,
    },
    gradient: {
      show: true,
      inset: "inset-0",
      rounded: "rounded-none",
      radialOpacity: "0%",
      linearOpacity: "30%",
      linearOpacityMobile: "60%",
      radialColor: theme.backgrounds.gradient.radialColor,
      linearColor: colors.gradientLinear,
    },
    timeline: {
      lineColor: colors.timelineInactive,
      activeLineColor: colors.timelineActive,
      itemBackground: colors.cardInactive,
      itemActiveBackground: colors.cardActive,
      dotColor: colors.timelineInactive,
      dotActiveColor: colors.timelineActive,
    },
    typography: {
      titleClassName: `${theme.typography.title.className} ${colors.textPrimary}`,
      descriptionClassName: `${theme.typography.description.className} ${colors.textSecondary}`,
    },
    colors: {
      textPrimary: colors.textPrimary,
      textSecondary: colors.textSecondary,
      border: colors.border,
      shadow: colors.shadow,
    },
    spacing: {
      gap: "gap-[var(--width-30)] md:gap-30",
      padding: "p-3",
      margin: "",
    },
    effects: {
      backdrop: theme.effects.backdrop,
      glow: theme.effects.glow,
      glass: theme.effects.glass,
    },
    card: {
      background: `${theme.effects.glass} ${colors.shadow}`,
      activeBackground: `${colors.cardActive} ${theme.effects.backdrop} ${colors.shadow}`,
      border: `${colors.border} rounded`,
      shadow: colors.shadow,
      imageContainer: `${theme.effects.glass} rounded`,
    },
    media: {
      borderRadius: "rounded",
      shadow: colors.shadow,
    },
  };
}
