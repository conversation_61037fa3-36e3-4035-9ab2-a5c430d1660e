import { RegularTimelineStyle } from "../shared/types";
import { funAndTrendyTimelineTheme as theme } from "../shared/themes";
import { ColorTemplate } from "../shared/themeConfig";
import { getFunAndTrendyTimelineColors } from "../shared/themeConfig";

export function getFunAndTrendyTimelineStyle(
  colorTemplate: ColorTemplate = 1
): RegularTimelineStyle {
  const colors = getFunAndTrendyTimelineColors(colorTemplate);
  
  return {
    section: {
      className: `${theme.spacing.sectionPadding} relative overflow-visible px-[var(--width-10)]`,
      backgroundColor: colors.primary,
      showBorder: true,
      fadeBottom: false,
    },
    gradient: {
      show: false,
      inset: "inset-0",
      rounded: "rounded-none",
      radialOpacity: "0%",
      linearOpacity: "0%",
      radialColor: "",
      linearColor: "",
    },
    timeline: {
      lineColor: colors.timelineInactive,
      activeLineColor: colors.timelineActive,
      itemBackground: colors.cardInactive,
      itemActiveBackground: colors.cardActive,
      dotColor: colors.timelineInactive,
      dotActiveColor: colors.timelineActive,
    },
    typography: {
      titleClassName: `${theme.typography.title.className} ${colors.textPrimary}`,
      descriptionClassName: `${theme.typography.description.className} ${colors.textPrimary}`,
    },
    colors: {
      textPrimary: colors.textPrimary,
      textSecondary: colors.textSecondary,
      border: colors.border,
      shadow: colors.shadow,
    },
    spacing: {
      gap: "gap-[var(--width-30)] md:gap-30",
      padding: "p-3",
      margin: "",
    },
    card: {
      background: `${colors.cardInactive} ${colors.shadow}`,
      activeBackground: `${colors.cardActive} ${colors.shadow}`,
      border: `${colors.border} rounded`,
      shadow: colors.shadow,
      imageContainer: `${colors.cardInactive} rounded`,
    },
    media: {
      borderRadius: "rounded",
      shadow: colors.shadow,
    },
  };
}
