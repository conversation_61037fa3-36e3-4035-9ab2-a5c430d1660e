import { YearTimelineStyle } from "../shared/types";
import { funAndTrendyTimelineTheme as theme } from "../shared/themes";
import { ColorTemplate } from "../shared/themeConfig";
import { getFunAndTrendyTimelineColors } from "../shared/themeConfig";

export function getFunAndTrendyYearTimelineStyle(
  colorTemplate: ColorTemplate = 1
): YearTimelineStyle {
  const colors = getFunAndTrendyTimelineColors(colorTemplate);
  
  return {
    section: {
      className: `${theme.spacing.sectionPadding} overflow-visible px-[var(--width-10)]`,
      backgroundColor: colors.primary,
      showBorder: true,
      fadeBottom: false,
    },
    gradient: {
      show: false,
      inset: "inset-0",
      rounded: "rounded-none",
      radialOpacity: "0%",
      linearOpacity: "0%",
      radialColor: "",
      linearColor: "",
    },
    timeline: {
      lineColor: colors.timelineInactive,
      activeLineColor: colors.timelineActive,
      itemBackground: colors.cardInactive,
      itemActiveBackground: colors.cardActive,
      dotColor: colors.timelineInactive,
      dotActiveColor: colors.timelineActive,
    },
    typography: {
      titleClassName: `${theme.typography.title.className} ${colors.textPrimary}`,
      descriptionClassName: `${theme.typography.description.className} ${colors.textPrimary}`,
      yearClassName: `${theme.typography.year.className} ${colors.textSecondary}`,
    },
    colors: {
      textPrimary: colors.textPrimary,
      textSecondary: colors.textSecondary,
      border: colors.border,
      shadow: colors.shadow,
    },
    spacing: {
      gap: theme.spacing.timelineGap,
      padding: "p-[calc(var(--width-10)/2)] pl-[var(--width-10)] md:pl-[calc(var(--width-10)/2)]",
      margin: "mb-[var(--width-10)] md:mb-10 last:mb-0",
    },
    yearDisplay: {
      className: `${theme.typography.year.className} ${colors.textSecondary}`,
      textColor: colors.textSecondary,
      backgroundColor: colors.secondary,
    },
  };
}
