import { YearTimelineStyle } from "../shared/types";
import { futuristicTimelineTheme as theme } from "../shared/themes";
import { ColorTemplate } from "../shared/themeConfig";
import { getFuturisticTimelineColors } from "../shared/themeConfig";

export function getFuturisticYearTimelineStyle(
  colorTemplate: ColorTemplate = 1
): YearTimelineStyle {
  const colors = getFuturisticTimelineColors(colorTemplate);
  
  return {
    section: {
      className: `${theme.spacing.sectionPadding} overflow-visible px-[var(--width-10)]`,
      backgroundColor: colors.primary,
      showBorder: false,
      fadeBottom: true,
    },
    gradient: {
      show: true,
      inset: "inset-0",
      rounded: "rounded-none",
      radialOpacity: "0%",
      linearOpacity: "30%",
      linearOpacityMobile: "60%",
      radialColor: theme.backgrounds.gradient.radialColor,
      linearColor: colors.gradientLinear,
    },
    timeline: {
      lineColor: colors.timelineInactive,
      activeLineColor: colors.timelineActive,
      itemBackground: colors.cardInactive,
      itemActiveBackground: colors.cardActive,
      dotColor: colors.timelineInactive,
      dotActiveColor: colors.timelineActive,
    },
    typography: {
      titleClassName: `${theme.typography.title.className} ${colors.textPrimary}`,
      descriptionClassName: `${theme.typography.description.className} ${colors.textSecondary}`,
      yearClassName: `${theme.typography.year.className} ${colors.textPrimary}`,
    },
    colors: {
      textPrimary: colors.textPrimary,
      textSecondary: colors.textSecondary,
      border: colors.border,
      shadow: colors.shadow,
    },
    spacing: {
      gap: theme.spacing.timelineGap,
      padding: "p-[calc(var(--width-10)/2)] pl-[var(--width-10)] md:pl-[calc(var(--width-10)/2)]",
      margin: "mb-[var(--width-10)] md:mb-10 last:mb-0",
    },
    effects: {
      backdrop: theme.effects.backdrop,
      glow: theme.effects.glow,
      glass: theme.effects.glass,
    },
    yearDisplay: {
      className: `${theme.typography.year.className} ${colors.textPrimary}`,
      textColor: colors.textPrimary,
    },
  };
}
