import { ProcessTimelineStyle } from "../shared/types";
import { funAndTrendyTimelineTheme as theme } from "../shared/themes";
import { ColorTemplate } from "../shared/themeConfig";
import { getFunAndTrendyTimelineColors } from "../shared/themeConfig";

export function getFunAndTrendyProcessTimelineStyle(
  colorTemplate: ColorTemplate = 1
): ProcessTimelineStyle {
  const colors = getFunAndTrendyTimelineColors(colorTemplate);
  
  return {
    section: {
      className: `${theme.spacing.sectionPadding} h-fit p-0`,
      backgroundColor: colors.primary,
      showBorder: true,
      fadeBottom: false,
    },
    gradient: {
      show: false,
      inset: "inset-0",
      rounded: "rounded-none",
      radialOpacity: "0%",
      linearOpacity: "0%",
      radialColor: "",
      linearColor: "",
    },
    timeline: {
      lineColor: colors.timelineInactive,
      activeLineColor: colors.timelineActive,
      itemBackground: colors.cardInactive,
      itemActiveBackground: colors.cardActive,
      dotColor: colors.timelineInactive,
      dotActiveColor: colors.timelineActive,
    },
    typography: {
      titleClassName: `${theme.typography.title.className} ${colors.textPrimary}`,
      descriptionClassName: `${theme.typography.description.className} ${colors.textPrimary}`,
    },
    colors: {
      textPrimary: colors.textPrimary,
      textSecondary: colors.textSecondary,
      border: colors.border,
      shadow: colors.shadow,
    },
    spacing: {
      gap: "gap-20",
      padding: "px-[var(--width-10)]",
      margin: "",
    },
    processStep: {
      numberBackground: colors.button,
      numberTextColor: colors.textSecondary,
      iconBackground: colors.cardInactive,
      iconTextColor: colors.textPrimary,
    },
    card: {
      background: colors.cardInactive,
      activeBackground: colors.cardActive,
      border: `${colors.border} rounded-lg`,
      shadow: colors.shadow,
    },
  };
}
