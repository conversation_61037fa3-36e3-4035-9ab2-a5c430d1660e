import { ProcessTimelineStyle } from "../shared/types";
import { futuristicTimelineTheme as theme } from "../shared/themes";
import { ColorTemplate } from "../shared/themeConfig";
import { getFuturisticTimelineColors } from "../shared/themeConfig";

export function getFuturisticProcessTimelineStyle(
  colorTemplate: ColorTemplate = 1
): ProcessTimelineStyle {
  const colors = getFuturisticTimelineColors(colorTemplate);
  
  return {
    section: {
      className: `${theme.spacing.sectionPadding} h-fit p-0`,
      backgroundColor: colors.primary,
      showBorder: false,
      fadeBottom: true,
    },
    gradient: {
      show: true,
      inset: "inset-0",
      rounded: "rounded-none",
      radialOpacity: "0%",
      linearOpacity: "30%",
      linearOpacityMobile: "60%",
      radialColor: theme.backgrounds.gradient.radialColor,
      linearColor: colors.gradientLinear,
    },
    timeline: {
      lineColor: colors.timelineInactive,
      activeLineColor: colors.timelineActive,
      itemBackground: colors.cardInactive,
      itemActiveBackground: colors.cardActive,
      dotColor: colors.timelineInactive,
      dotActiveColor: colors.timelineActive,
    },
    typography: {
      titleClassName: `${theme.typography.title.className} ${colors.textPrimary}`,
      descriptionClassName: `${theme.typography.description.className} ${colors.textSecondary}`,
    },
    colors: {
      textPrimary: colors.textPrimary,
      textSecondary: colors.textSecondary,
      border: colors.border,
      shadow: colors.shadow,
    },
    spacing: {
      gap: "gap-20",
      padding: "px-[var(--width-10)]",
      margin: "",
    },
    effects: {
      backdrop: theme.effects.backdrop,
      glow: theme.effects.glow,
      glass: theme.effects.glass,
    },
    processStep: {
      numberBackground: colors.timelineActive,
      numberTextColor: colors.textPrimary,
      iconBackground: `${theme.effects.glass}`,
      iconTextColor: colors.textPrimary,
    },
    card: {
      background: theme.effects.glass,
      activeBackground: `${colors.cardActive} ${theme.effects.backdrop}`,
      border: `${colors.border} rounded-lg`,
      shadow: colors.shadow,
    },
  };
}
